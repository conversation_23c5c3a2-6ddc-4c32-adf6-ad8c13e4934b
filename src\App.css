/* App 主要佈局 */

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  width: 100%;
}

/* 全域組件樣式 */
@layer components {
  h1 {
    color: var(--accent);
    font-family: Troubleside, Arial, sans-serif;
    margin: 1rem 0 0;
  }
}

/* 工具類別 */
@layer utilities {
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  .text-center {
    text-align: center;
  }

  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
}
