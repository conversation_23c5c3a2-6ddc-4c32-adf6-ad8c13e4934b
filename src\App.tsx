import { useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navigation from './components/Navigation';
import Footer from './components/Footer';
import Home from './pages/Home';
import Browse from './pages/Browse';
import Subscriptions from './pages/Subscriptions';
import About from './pages/About';
import Feedback from './pages/Feedback';
import Help from './pages/Help';
import Terms from './pages/Terms';
import './App.css';

function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  const handleLogin = () => {
    // 這裡應該實現真正的登入邏輯
    setIsLoggedIn(true);
    // 模擬管理員登入
    setIsAdmin(true);
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setIsAdmin(false);
  };

  return (
    <Router>
      <div className="app">
        <Navigation
          isLoggedIn={isLoggedIn}
          isAdmin={isAdmin}
          onLogin={handleLogin}
          onLogout={handleLogout}
        />

        <main className="main-content">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/free" element={<Browse />} />
            <Route path="/series" element={<Browse />} />
            <Route path="/categories" element={<Browse />} />
            <Route path="/subscriptions" element={<Subscriptions />} />
            <Route path="/about" element={<About />} />
            <Route path="/feedback" element={<Feedback />} />
            <Route path="/help" element={<Help />} />
            <Route path="/terms" element={<Terms />} />
            <Route path="/privacy" element={<Terms />} />
            <Route path="/advertising" element={<Terms />} />
            <Route path="/licensing" element={<Terms />} />
            {/* 其他路由可以在這裡添加 */}
          </Routes>
        </main>

        <Footer />
      </div>
    </Router>
  );
}

export default App;
