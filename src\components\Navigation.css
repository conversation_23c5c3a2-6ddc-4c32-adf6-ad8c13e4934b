/* Navigation Styles - 漫畫屋風格 */

.navigation {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: var(--surface);
  border-bottom: var(--border-size) solid var(--accent);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px oklch(from var(--accent) 0.2 c h / 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

/* Brand/Logo */
.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: var(--accent);
  font-family: Troubleside, Arial, sans-serif;
  font-size: 1.5rem;
  font-weight: bold;
  transition: all var(--t) ease-in-out;
}

.nav-brand:hover {
  transform: scale(1.05);
  text-shadow: 0 0 10px var(--accent);
}

.brand-icon {
  font-size: 1.8rem;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-3px); }
}

/* Desktop Navigation Links */
.nav-links {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: var(--text);
  border-radius: var(--radius);
  transition: all var(--t) ease-in-out;
  position: relative;
  font-weight: 500;
}

.nav-link:hover {
  background: oklch(from var(--accent) l c h / 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px oklch(from var(--accent) 0.2 c h / 0.2);
}

.nav-link.active {
  background: var(--accent);
  color: var(--surface);
  box-shadow: 0 2px 8px oklch(from var(--accent) 0.3 c h / 0.3);
}

.nav-link.active::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 2px;
  background: var(--accent);
  border-radius: 1px;
}

.nav-icon {
  font-size: 1.1rem;
}

.nav-label {
  font-size: 0.9rem;
}

/* Admin Section */
.admin-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 1rem;
  padding-left: 1rem;
  border-left: 1px solid oklch(from var(--accent) l c h / 0.3);
}

.admin-link {
  background: oklch(from var(--accent) 0.1 c h / 0.1);
  border: 1px solid oklch(from var(--accent) l c h / 0.3);
}

.admin-link:hover {
  background: oklch(from var(--accent) 0.15 c h / 0.2);
}

/* User Actions */
.nav-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* User Menu */
.user-menu {
  position: relative;
}

.user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  border: 2px solid var(--accent);
  background: var(--surface);
  color: var(--accent);
  font-size: 1.2rem;
  cursor: pointer;
  transition: all var(--t) ease-in-out;
}

.user-avatar:hover {
  transform: scale(1.1);
  box-shadow: 0 0 15px oklch(from var(--accent) l c h / 0.3);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: var(--surface);
  border: var(--border-size) solid var(--accent);
  border-radius: var(--radius);
  box-shadow: 0 8px 24px oklch(from var(--accent) 0.2 c h / 0.2);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--t) ease-in-out;
  min-width: 150px;
}

.user-menu:hover .user-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: block;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: var(--text);
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  transition: background var(--t) ease-in-out;
}

.dropdown-item:hover {
  background: oklch(from var(--accent) l c h / 0.1);
}

.logout-btn {
  border-top: 1px solid oklch(from var(--accent) l c h / 0.2);
  color: #ff6b6b;
}

/* Auth Buttons */
.auth-buttons {
  display: flex;
  gap: 0.5rem;
}

.login-btn, .register-btn {
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  text-decoration: none;
  font-weight: 500;
  transition: all var(--t) ease-in-out;
  border: none;
  cursor: pointer;
}

.login-btn {
  background: transparent;
  color: var(--accent);
  border: 1px solid var(--accent);
}

.login-btn:hover {
  background: var(--accent);
  color: var(--surface);
}

.register-btn {
  background: var(--accent);
  color: var(--surface);
}

.register-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px oklch(from var(--accent) 0.3 c h / 0.3);
}

/* Mobile Menu Button */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  z-index: 1001;
}

.hamburger {
  display: flex;
  flex-direction: column;
  width: 24px;
  height: 18px;
  position: relative;
}

.hamburger span {
  display: block;
  height: 2px;
  width: 100%;
  background: var(--accent);
  border-radius: 1px;
  transition: all 0.3s ease-in-out;
  transform-origin: center;
}

.hamburger span:nth-child(1) {
  margin-bottom: 6px;
}

.hamburger span:nth-child(2) {
  margin-bottom: 6px;
}

.hamburger.open span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger.open span:nth-child(2) {
  opacity: 0;
}

.hamburger.open span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Menu */
.mobile-menu {
  position: fixed;
  top: 4rem;
  left: 0;
  right: 0;
  background: var(--surface);
  border-bottom: var(--border-size) solid var(--accent);
  transform: translateY(-100%);
  transition: transform 0.3s ease-in-out;
  z-index: 999;
  max-height: calc(100vh - 4rem);
  overflow-y: auto;
}

.mobile-menu.open {
  transform: translateY(0);
}

.mobile-nav-links {
  padding: 1rem;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  text-decoration: none;
  color: var(--text);
  border-radius: var(--radius);
  margin-bottom: 0.5rem;
  transition: all var(--t) ease-in-out;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.mobile-nav-link:hover {
  background: oklch(from var(--accent) l c h / 0.1);
}

.mobile-nav-link.active {
  background: var(--accent);
  color: var(--surface);
}

.mobile-divider {
  height: 1px;
  background: oklch(from var(--accent) l c h / 0.2);
  margin: 1rem 0;
}

.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 1rem;
  }

  .nav-links,
  .nav-actions .auth-buttons,
  .user-dropdown {
    display: none;
  }

  .mobile-menu-btn {
    display: block;
  }

  .nav-actions .user-avatar {
    display: block;
  }
}

@media (max-width: 480px) {
  .nav-container {
    height: 3.5rem;
  }

  .nav-brand {
    font-size: 1.2rem;
  }

  .brand-icon {
    font-size: 1.5rem;
  }
}
