import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import './Navigation.css';

interface NavigationProps {
  isLoggedIn?: boolean;
  isAdmin?: boolean;
  onLogin?: () => void;
  onLogout?: () => void;
}

const Navigation: React.FC<NavigationProps> = ({ 
  isLoggedIn = false, 
  isAdmin = false, 
  onLogin, 
  onLogout 
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();

  const navigationItems = [
    { path: '/', label: '首頁', icon: '🏠' },
    { path: '/browse', label: '瀏覽漫畫', icon: '📚' },
    { path: '/subscriptions', label: '我的訂閱', icon: '⭐', requiresAuth: true },
    { path: '/about', label: '關於我們', icon: 'ℹ️' },
  ];

  const adminItems = [
    { path: '/admin', label: '管理面板', icon: '⚙️' },
    { path: '/admin/upload', label: '上傳作品', icon: '📤' },
  ];

  const isActivePath = (path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <nav className="navigation">
      <div className="nav-container">
        {/* Logo/Brand */}
        <Link to="/" className="nav-brand">
          <span className="brand-icon">📖</span>
          <span className="brand-text">漫畫屋</span>
        </Link>

        {/* Desktop Navigation */}
        <div className="nav-links">
          {navigationItems.map((item) => {
            if (item.requiresAuth && !isLoggedIn) return null;
            
            return (
              <Link
                key={item.path}
                to={item.path}
                className={`nav-link ${isActivePath(item.path) ? 'active' : ''}`}
              >
                <span className="nav-icon">{item.icon}</span>
                <span className="nav-label">{item.label}</span>
              </Link>
            );
          })}

          {/* Admin Links */}
          {isLoggedIn && isAdmin && (
            <div className="admin-section">
              {adminItems.map((item) => (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`nav-link admin-link ${isActivePath(item.path) ? 'active' : ''}`}
                >
                  <span className="nav-icon">{item.icon}</span>
                  <span className="nav-label">{item.label}</span>
                </Link>
              ))}
            </div>
          )}
        </div>

        {/* User Actions */}
        <div className="nav-actions">
          {isLoggedIn ? (
            <div className="user-menu">
              <button className="user-avatar">👤</button>
              <div className="user-dropdown">
                <Link to="/profile" className="dropdown-item">個人資料</Link>
                <Link to="/settings" className="dropdown-item">設定</Link>
                <button onClick={onLogout} className="dropdown-item logout-btn">
                  登出
                </button>
              </div>
            </div>
          ) : (
            <div className="auth-buttons">
              <button onClick={onLogin} className="login-btn">登入</button>
              <Link to="/register" className="register-btn">註冊</Link>
            </div>
          )}
        </div>

        {/* Mobile Menu Button */}
        <button 
          className="mobile-menu-btn"
          onClick={toggleMobileMenu}
          aria-label="開啟選單"
        >
          <span className={`hamburger ${isMobileMenuOpen ? 'open' : ''}`}>
            <span></span>
            <span></span>
            <span></span>
          </span>
        </button>
      </div>

      {/* Mobile Menu */}
      <div className={`mobile-menu ${isMobileMenuOpen ? 'open' : ''}`}>
        <div className="mobile-nav-links">
          {navigationItems.map((item) => {
            if (item.requiresAuth && !isLoggedIn) return null;
            
            return (
              <Link
                key={item.path}
                to={item.path}
                className={`mobile-nav-link ${isActivePath(item.path) ? 'active' : ''}`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <span className="nav-icon">{item.icon}</span>
                <span className="nav-label">{item.label}</span>
              </Link>
            );
          })}

          {/* Mobile Admin Links */}
          {isLoggedIn && isAdmin && (
            <>
              <div className="mobile-divider"></div>
              {adminItems.map((item) => (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`mobile-nav-link admin-link ${isActivePath(item.path) ? 'active' : ''}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <span className="nav-icon">{item.icon}</span>
                  <span className="nav-label">{item.label}</span>
                </Link>
              ))}
            </>
          )}

          {/* Mobile Auth Section */}
          <div className="mobile-divider"></div>
          {isLoggedIn ? (
            <div className="mobile-user-section">
              <Link to="/profile" className="mobile-nav-link" onClick={() => setIsMobileMenuOpen(false)}>
                <span className="nav-icon">👤</span>
                <span className="nav-label">個人資料</span>
              </Link>
              <Link to="/settings" className="mobile-nav-link" onClick={() => setIsMobileMenuOpen(false)}>
                <span className="nav-icon">⚙️</span>
                <span className="nav-label">設定</span>
              </Link>
              <button 
                onClick={() => {
                  onLogout?.();
                  setIsMobileMenuOpen(false);
                }} 
                className="mobile-nav-link logout-btn"
              >
                <span className="nav-icon">🚪</span>
                <span className="nav-label">登出</span>
              </button>
            </div>
          ) : (
            <div className="mobile-auth-section">
              <button 
                onClick={() => {
                  onLogin?.();
                  setIsMobileMenuOpen(false);
                }} 
                className="mobile-nav-link login-btn"
              >
                <span className="nav-icon">🔑</span>
                <span className="nav-label">登入</span>
              </button>
              <Link 
                to="/register" 
                className="mobile-nav-link register-btn"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <span className="nav-icon">📝</span>
                <span className="nav-label">註冊</span>
              </Link>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div 
          className="mobile-menu-overlay"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </nav>
  );
};

export default Navigation;
