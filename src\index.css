/* 漫畫屋 - 全域樣式系統 */

@layer base, tokens, animations, components, utilities;

@layer tokens {
  @font-face {
    font-family: Troubleside;
    src: url("https://cdn.jsdelivr.net/gh/chytanka/chytanka.github.io/src/assets/fonts/Troubleside.ttf");
    font-display: swap;
  }

  :root {
    --ratio: 257 / 364;
    --t: 0.133333334s;
    --min-card-width: 192px;
    --accent: oklch(from #ffd60a l c h);
    --surface: oklch(from #166496 0.12 min(c, 0.0512) h);

    --avarage-l: 0.48;
    --avarage-l-2: 0.36;
    --avarage-base: oklch(from #ffd60a var(--avarage-l) 0.0192 h);
    --text: white;

    --radius: 0.5rem;
    --border-size: 0.25rem;

    background: var(--surface);
    color: var(--text);
    font-size: clamp(0.875rem, 0.8214285714rem + 0.2380952381vw, 1rem) !important;
    line-height: 1.3;
    font-family: Arial, sans-serif;
  }

  @media (prefers-color-scheme: light) {
    :root {
      --avarage-l: 0.72;
      --avarage-l-2: 0.6;
      --accent: oklch(from #166496 l c h);
      --surface: oklch(from #ffd60a 1 min(c, 0.0256) h);
      --avarage-base: oklch(from #ffd60a 0.48 0.0192 h);
      --text: black;
    }
  }

  @property --item-scale {
    syntax: "<number>";
    inherits: true;
    initial-value: 1;
  }

  @property --item-angle {
    syntax: "<angle>";
    inherits: true;
    initial-value: 0deg;
  }
}

@layer animations {
  @keyframes shrink-top {
    0% {
      --item-scale: 0.5;
      --item-angle: -60deg;
      transform-origin: top;
    }
  }

  @keyframes shrink-bottom {
    0% {
      --item-scale: 0.5;
      --item-angle: 60deg;
      transform-origin: bottom;
    }
  }
}

@layer base {
  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }

  body,
  figure {
    margin: 0;
  }

  body {
    min-height: 100vh;
    background: var(--surface);
    color: var(--text);
    font-family: Arial, sans-serif;
    line-height: 1.3;
  }

  img {
    width: 100%;
    height: auto;
    display: block;
  }

  a {
    color: inherit;
    text-decoration: none;
  }

  button {
    font-family: inherit;
    cursor: pointer;
  }

  input, select, textarea {
    font-family: inherit;
  }
}
