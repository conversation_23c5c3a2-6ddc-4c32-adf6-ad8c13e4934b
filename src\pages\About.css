/* About Page Styles */

.about-page {
  min-height: 100vh;
  padding: 2rem;
}

.about-container {
  max-width: 1000px;
  margin: 0 auto;
}

.about-header {
  text-align: center;
  margin-bottom: 4rem;
}

.about-header h1 {
  color: var(--accent);
  font-family: Troubleside, Arial, sans-serif;
  font-size: 3rem;
  margin-bottom: 1rem;
}

.about-header p {
  color: var(--text);
  opacity: 0.8;
  font-size: 1.3rem;
}

.about-content {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.about-section {
  background: oklch(from var(--accent) l c h / 0.05);
  border: var(--border-size) solid oklch(from var(--accent) l c h / 0.2);
  border-radius: var(--radius);
  padding: 2.5rem;
  transition: all var(--t) ease-in-out;
}

.about-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px oklch(from var(--accent) 0.2 c h / 0.1);
}

.about-section h2 {
  color: var(--accent);
  font-family: Troubleside, Arial, sans-serif;
  font-size: 2rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}



.about-section p {
  color: var(--text);
  font-size: 1.1rem;
  line-height: 1.6;
  opacity: 0.9;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.feature-card {
  background: var(--surface);
  border: var(--border-size) solid var(--accent);
  border-radius: var(--radius);
  padding: 2rem;
  text-align: center;
  transition: all var(--t) ease-in-out;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--accent), oklch(from var(--accent) l c h / 0.5));
}

.feature-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 32px oklch(from var(--accent) 0.3 c h / 0.2);
}



.feature-card h3 {
  color: var(--accent);
  font-family: Troubleside, Arial, sans-serif;
  font-size: 1.3rem;
  margin-bottom: 1rem;
}

.feature-card p {
  color: var(--text);
  opacity: 0.8;
  line-height: 1.5;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 2rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: oklch(from var(--accent) l c h / 0.05);
  border-radius: var(--radius);
  transition: all var(--t) ease-in-out;
}

.contact-item:hover {
  background: oklch(from var(--accent) l c h / 0.1);
  transform: translateX(4px);
}

.contact-label {
  font-weight: 600;
  color: var(--accent);
  min-width: 6rem;
}

.contact-item span:last-child {
  color: var(--text);
  font-weight: 500;
}

/* 動畫效果 */
.about-section {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease-out forwards;
}

.about-section:nth-child(1) { animation-delay: 0.1s; }
.about-section:nth-child(2) { animation-delay: 0.2s; }
.about-section:nth-child(3) { animation-delay: 0.3s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-card {
  opacity: 0;
  transform: scale(0.9);
  animation: scaleIn 0.5s ease-out forwards;
}

.feature-card:nth-child(1) { animation-delay: 0.4s; }
.feature-card:nth-child(2) { animation-delay: 0.5s; }
.feature-card:nth-child(3) { animation-delay: 0.6s; }
.feature-card:nth-child(4) { animation-delay: 0.7s; }

@keyframes scaleIn {
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 響應式設計 */
@media (max-width: 768px) {
  .about-page {
    padding: 1rem;
  }

  .about-header h1 {
    font-size: 2.5rem;
  }

  .about-header p {
    font-size: 1.1rem;
  }

  .about-section {
    padding: 2rem;
  }

  .about-section h2 {
    font-size: 1.5rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .feature-card {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .about-header h1 {
    font-size: 2rem;
  }

  .about-section {
    padding: 1.5rem;
  }

  .about-section h2 {
    font-size: 1.3rem;
  }



  .contact-item {
    padding: 0.75rem;
  }
}
