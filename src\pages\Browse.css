/* Browse Page Styles */

.browse-page {
  min-height: 100vh;
  padding: 2rem;
}

.browse-container {
  max-width: 1200px;
  margin: 0 auto;
}

.browse-header {
  text-align: center;
  margin-bottom: 2rem;
}

.browse-header h1 {
  color: var(--accent);
  font-family: Troubleside, Arial, sans-serif;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.browse-header p {
  color: var(--text);
  opacity: 0.8;
  font-size: 1.1rem;
}

.browse-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  align-items: end;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  border: var(--border-size) solid var(--accent);
  border-radius: var(--radius);
  background: var(--surface);
  color: var(--text);
  font-size: 1rem;
  transition: all var(--t) ease-in-out;
}

.search-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px oklch(from var(--accent) l c h / 0.3);
  transform: translateY(-1px);
}

.search-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--accent);
  pointer-events: none;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.filter-group label {
  font-size: 0.9rem;
  color: var(--text);
  opacity: 0.8;
}

.filter-select {
  padding: 0.75rem;
  border: var(--border-size) solid var(--accent);
  border-radius: var(--radius);
  background: var(--surface);
  color: var(--text);
  font-size: 0.9rem;
  min-width: 120px;
  cursor: pointer;
  transition: all var(--t) ease-in-out;
}

.filter-select:focus {
  outline: none;
  box-shadow: 0 0 0 2px oklch(from var(--accent) l c h / 0.3);
}

.results-info {
  margin-bottom: 1.5rem;
  color: var(--text);
  opacity: 0.7;
  font-size: 0.9rem;
}

.manga-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
}

.browse-manga-card {
  --avarage: oklch(from var(--avarage-color) var(--avarage-l) 0.0192 h);
  --shadow-color: oklch(from var(--avarage, var(--avarage-base)) var(--avarage-l-2) c h);
  
  background: var(--surface);
  border: var(--border-size) solid var(--avarage, var(--avarage-base));
  border-radius: var(--radius);
  overflow: hidden;
  transition: all var(--t) ease-in-out;
  cursor: pointer;
  position: relative;
}

.browse-manga-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px oklch(from var(--avarage, var(--avarage-base)) 0.3 c h / 0.3);
}

.card-image {
  position: relative;
  aspect-ratio: var(--ratio);
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--t) ease-in-out;
}

.browse-manga-card:hover .card-image img {
  transform: scale(1.05);
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.8) 100%);
  display: flex;
  align-items: end;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  opacity: 0;
  transition: opacity var(--t) ease-in-out;
}

.browse-manga-card:hover .card-overlay {
  opacity: 1;
}

.quick-action, .subscribe-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: var(--radius);
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--t) ease-in-out;
}

.quick-action {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
}

.quick-action:hover {
  background: rgba(255, 255, 255, 0.3);
}

.subscribe-btn {
  background: var(--accent);
  color: var(--surface);
}

.subscribe-btn:hover {
  transform: scale(1.05);
}

.card-content {
  padding: 1.5rem;
}

.card-title {
  font-family: Troubleside, Arial, sans-serif;
  font-size: 1.1rem;
  margin-bottom: 0.75rem;
  color: var(--text);
  line-height: 1.3;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.card-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  color: white;
}

.rating {
  font-size: 0.9rem;
  color: var(--accent);
  font-weight: 500;
}

.card-genres {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;
}

.genre-tag {
  padding: 0.25rem 0.5rem;
  background: oklch(from var(--accent) l c h / 0.1);
  color: var(--accent);
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.card-description {
  color: var(--text);
  opacity: 0.8;
  font-size: 0.9rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

.no-results {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--text);
  opacity: 0.6;
}



.no-results h3 {
  font-family: Troubleside, Arial, sans-serif;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.no-results p {
  font-size: 1rem;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .browse-page {
    padding: 1rem;
  }

  .browse-filters {
    flex-direction: column;
    gap: 1rem;
  }

  .search-box {
    min-width: auto;
  }

  .filter-group {
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
  }

  .filter-group label {
    min-width: 60px;
  }

  .manga-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .browse-header h1 {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .manga-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .card-content {
    padding: 1rem;
  }

  .browse-header h1 {
    font-size: 1.5rem;
  }
}
