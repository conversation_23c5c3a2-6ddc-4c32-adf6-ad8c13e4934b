import React, { useState } from 'react';
import './Browse.css';

interface MangaItem {
  id: string;
  title: string;
  image: string;
  averageColor: string;
  genre: string[];
  status: 'ongoing' | 'completed' | 'hiatus';
  rating: number;
  description: string;
}

const Browse: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGenre, setSelectedGenre] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  const allManga: MangaItem[] = [
    {
      id: '1',
      title: 'Frieren: Beyond Journey\'s End',
      image: 'https://i.imgur.com/HWxOtcQ.jpeg',
      averageColor: '#b0b6a9',
      genre: ['Fantasy', 'Adventure', 'Drama'],
      status: 'ongoing',
      rating: 9.2,
      description: '在勇者一行人打倒魔王後的故事，精靈法師芙莉蓮開始了新的旅程。'
    },
    {
      id: '2',
      title: '<PERSON>dad<PERSON>',
      image: 'https://i.imgur.com/7FQ6L5j.jpeg',
      averageColor: '#b47460',
      genre: ['Supernatural', 'Comedy', 'Action'],
      status: 'ongoing',
      rating: 8.8,
      description: '關於超自然現象和外星人的奇幻冒險故事。'
    },
    {
      id: '3',
      title: 'My Broken Mariko',
      image: 'https://i.imgur.com/OS0VRhm.png',
      averageColor: '#6e695e',
      genre: ['Drama', 'Psychological'],
      status: 'completed',
      rating: 9.0,
      description: '一個關於友情、失去和治癒的深刻故事。'
    },
    {
      id: '4',
      title: 'BLAME!',
      image: 'https://i.imgur.com/yCBmW1b.png',
      averageColor: '#7b4d35',
      genre: ['Sci-Fi', 'Action', 'Dystopian'],
      status: 'completed',
      rating: 8.5,
      description: '在巨大建築物中尋找網路終端基因的科幻冒險。'
    }
  ];

  const genres = ['all', 'Fantasy', 'Adventure', 'Drama', 'Supernatural', 'Comedy', 'Action', 'Psychological', 'Sci-Fi', 'Dystopian'];
  const statuses = ['all', 'ongoing', 'completed', 'hiatus'];

  const filteredManga = allManga.filter(manga => {
    const matchesSearch = manga.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         manga.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesGenre = selectedGenre === 'all' || manga.genre.includes(selectedGenre);
    const matchesStatus = selectedStatus === 'all' || manga.status === selectedStatus;
    
    return matchesSearch && matchesGenre && matchesStatus;
  });

  const getStatusText = (status: string) => {
    switch (status) {
      case 'ongoing': return '連載中';
      case 'completed': return '已完結';
      case 'hiatus': return '休載中';
      default: return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ongoing': return '#4ade80';
      case 'completed': return '#60a5fa';
      case 'hiatus': return '#fbbf24';
      default: return '#6b7280';
    }
  };

  return (
    <div className="browse-page">
      <div className="browse-container">
        <header className="browse-header">
          <h1>瀏覽漫畫</h1>
          <p>探索我們豐富的漫畫收藏</p>
        </header>

        <div className="browse-filters">
          <div className="search-box">
            <input
              type="text"
              placeholder="搜尋漫畫標題或描述..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
            <span className="search-icon">搜尋</span>
          </div>

          <div className="filter-group">
            <label htmlFor="genre-select">類型：</label>
            <select
              id="genre-select"
              value={selectedGenre}
              onChange={(e) => setSelectedGenre(e.target.value)}
              className="filter-select"
            >
              {genres.map(genre => (
                <option key={genre} value={genre}>
                  {genre === 'all' ? '全部類型' : genre}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="status-select">狀態：</label>
            <select
              id="status-select"
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="filter-select"
            >
              {statuses.map(status => (
                <option key={status} value={status}>
                  {status === 'all' ? '全部狀態' : getStatusText(status)}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="results-info">
          <span>找到 {filteredManga.length} 部漫畫</span>
        </div>

        <div className="manga-grid">
          {filteredManga.map((manga, index) => (
            <article
              key={manga.id}
              className={`browse-manga-card browse-card-${index + 1}`}
              style={{ '--avarage-color': manga.averageColor } as React.CSSProperties}
            >
              <div className="card-image">
                <img src={manga.image} alt={manga.title} loading="lazy" />
                <div className="card-overlay">
                  <button className="quick-action">快速預覽</button>
                  <button className="subscribe-btn">訂閱</button>
                </div>
              </div>
              
              <div className="card-content">
                <h3 className="card-title">{manga.title}</h3>
                
                <div className="card-meta">
                  <span 
                    className="status-badge"
                    style={{ backgroundColor: getStatusColor(manga.status) }}
                  >
                    {getStatusText(manga.status)}
                  </span>
                  <span className="rating">評分 {manga.rating}</span>
                </div>

                <div className="card-genres">
                  {manga.genre.slice(0, 3).map(genre => (
                    <span key={genre} className="genre-tag">{genre}</span>
                  ))}
                </div>

                <p className="card-description">{manga.description}</p>
              </div>
            </article>
          ))}
        </div>

        {filteredManga.length === 0 && (
          <div className="no-results">
            <h3>沒有找到符合條件的漫畫</h3>
            <p>試試調整搜尋條件或瀏覽其他類型</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Browse;
