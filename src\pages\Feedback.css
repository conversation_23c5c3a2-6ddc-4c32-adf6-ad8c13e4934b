/* Feedback Page Styles */

.feedback-page {
  min-height: 100vh;
  padding: 2rem;
}

.feedback-container {
  max-width: 1000px;
  margin: 0 auto;
}

.feedback-header {
  text-align: center;
  margin-bottom: 3rem;
}

.feedback-header h1 {
  color: var(--accent);
  font-family: Troubleside, Arial, sans-serif;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.feedback-header p {
  color: var(--text);
  opacity: 0.8;
  font-size: 1.1rem;
}

.feedback-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 3rem;
}

.feedback-info {
  background: oklch(from var(--accent) l c h / 0.05);
  border: var(--border-size) solid oklch(from var(--accent) l c h / 0.2);
  border-radius: var(--radius);
  padding: 2rem;
}

.feedback-info h3 {
  color: var(--accent);
  font-family: Troubleside, Arial, sans-serif;
  font-size: 1.3rem;
  margin-bottom: 1rem;
}

.feedback-info p {
  color: var(--text);
  opacity: 0.8;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.contact-methods h4 {
  color: var(--accent);
  font-size: 1.1rem;
  margin-bottom: 1rem;
}

.contact-item {
  margin-bottom: 0.75rem;
  color: var(--text);
  opacity: 0.8;
}

.contact-item strong {
  color: var(--accent);
  opacity: 1;
}

.contact-item a {
  color: var(--accent);
  text-decoration: none;
}

.contact-item a:hover {
  text-decoration: underline;
}

.feedback-form {
  background: var(--surface);
  border: var(--border-size) solid oklch(from var(--accent) l c h / 0.2);
  border-radius: var(--radius);
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  color: var(--text);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: var(--border-size) solid oklch(from var(--accent) l c h / 0.3);
  border-radius: var(--radius);
  background: var(--surface);
  color: var(--text);
  font-size: 1rem;
  transition: all var(--t) ease-in-out;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--accent);
  box-shadow: 0 0 0 2px oklch(from var(--accent) l c h / 0.2);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-btn {
  width: 100%;
  padding: 1rem;
  background: var(--accent);
  color: var(--surface);
  border: none;
  border-radius: var(--radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--t) ease-in-out;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px oklch(from var(--accent) 0.3 c h / 0.3);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .feedback-page {
    padding: 1rem;
  }

  .feedback-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .feedback-header h1 {
    font-size: 2rem;
  }

  .feedback-info,
  .feedback-form {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .feedback-header h1 {
    font-size: 1.5rem;
  }

  .feedback-info,
  .feedback-form {
    padding: 1rem;
  }
}
