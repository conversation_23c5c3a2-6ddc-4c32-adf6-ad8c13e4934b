/* Help Page Styles */

.help-page {
  min-height: 100vh;
  padding: 2rem;
}

.help-container {
  max-width: 800px;
  margin: 0 auto;
}

.help-header {
  text-align: center;
  margin-bottom: 3rem;
}

.help-header h1 {
  color: var(--accent);
  font-family: Troubleside, Arial, sans-serif;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.help-header p {
  color: var(--text);
  opacity: 0.8;
  font-size: 1.1rem;
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.faq-section h2,
.contact-section h2 {
  color: var(--accent);
  font-family: Troubleside, Arial, sans-serif;
  font-size: 1.8rem;
  margin-bottom: 2rem;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.faq-item {
  background: oklch(from var(--accent) l c h / 0.05);
  border: var(--border-size) solid oklch(from var(--accent) l c h / 0.2);
  border-radius: var(--radius);
  padding: 1.5rem;
  transition: all var(--t) ease-in-out;
}

.faq-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px oklch(from var(--accent) 0.2 c h / 0.1);
}

.faq-question {
  color: var(--accent);
  font-family: Troubleside, Arial, sans-serif;
  font-size: 1.2rem;
  margin-bottom: 0.75rem;
}

.faq-answer {
  color: var(--text);
  opacity: 0.8;
  line-height: 1.6;
  margin: 0;
}

.contact-section {
  background: var(--surface);
  border: var(--border-size) solid oklch(from var(--accent) l c h / 0.2);
  border-radius: var(--radius);
  padding: 2rem;
}

.contact-section p {
  color: var(--text);
  opacity: 0.8;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-item {
  color: var(--text);
  opacity: 0.8;
}

.contact-item strong {
  color: var(--accent);
  opacity: 1;
}

.contact-item a {
  color: var(--accent);
  text-decoration: none;
}

.contact-item a:hover {
  text-decoration: underline;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .help-page {
    padding: 1rem;
  }

  .help-header h1 {
    font-size: 2rem;
  }

  .faq-section h2,
  .contact-section h2 {
    font-size: 1.5rem;
  }

  .faq-item,
  .contact-section {
    padding: 1.25rem;
  }
}

@media (max-width: 480px) {
  .help-header h1 {
    font-size: 1.5rem;
  }

  .faq-question {
    font-size: 1.1rem;
  }

  .faq-item,
  .contact-section {
    padding: 1rem;
  }
}
