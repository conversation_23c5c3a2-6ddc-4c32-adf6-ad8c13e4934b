import React from 'react';
import './Help.css';

const Help: React.FC = () => {
  const faqItems = [
    {
      question: '如何註冊會員？',
      answer: '點擊右上角的「登入會員」按鈕，然後選擇「註冊新帳號」，填寫必要資訊即可完成註冊。'
    },
    {
      question: '如何訂閱漫畫？',
      answer: '在漫畫詳情頁面點擊「訂閱」按鈕，訂閱後您將在漫畫更新時收到通知。'
    },
    {
      question: '什麼是 Free 搶先閱？',
      answer: 'Free 搶先閱是免費提供的最新章節預覽，讓您搶先體驗精彩內容。'
    },
    {
      question: '如何搜尋漫畫？',
      answer: '使用頂部搜尋欄輸入漫畫名稱，或透過「作品分類」瀏覽不同類型的漫畫。'
    },
    {
      question: '忘記密碼怎麼辦？',
      answer: '在登入頁面點擊「忘記密碼」，輸入您的電子信箱，我們會發送重設密碼的連結給您。'
    }
  ];

  return (
    <div className="help-page">
      <div className="help-container">
        <header className="help-header">
          <h1>幫助中心</h1>
          <p>常見問題與使用指南</p>
        </header>

        <div className="help-content">
          <section className="faq-section">
            <h2>常見問題</h2>
            <div className="faq-list">
              {faqItems.map((item, index) => (
                <div key={index} className="faq-item">
                  <h3 className="faq-question">{item.question}</h3>
                  <p className="faq-answer">{item.answer}</p>
                </div>
              ))}
            </div>
          </section>

          <section className="contact-section">
            <h2>需要更多幫助？</h2>
            <p>如果您的問題沒有在上述列表中找到答案，請聯絡我們的客服團隊：</p>
            <div className="contact-info">
              <div className="contact-item">
                <strong>客服信箱：</strong>
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </div>
              <div className="contact-item">
                <strong>客服時間：</strong>
                <span>週一至週五 09:00-18:00</span>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};

export default Help;
