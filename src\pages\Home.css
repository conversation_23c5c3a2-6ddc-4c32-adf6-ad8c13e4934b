/* Home Page - 漫畫展示樣式 */

.home-page {
  min-height: 100vh;
}

.manga-main {
  padding: 2rem;
}

.manga-section {
  margin-bottom: 2rem;
  position: relative;
  perspective: 4000px;
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fill, minmax(var(--min-card-width), 1fr));
}

.manga-section header {
  grid-column: 1 / -1;
}

.manga-section h1 {
  color: var(--accent);
  font-family: Troubleside, Arial, sans-serif;
  margin: 1rem 0 0;
  font-size: 2rem;
}

.manga-card {
  --deg: 0.8deg;
  --y: 0;
  --avarage: oklch(from var(--avarage-color) var(--avarage-l) 0.0192 h);
  --shadow-color: oklch(
    from var(--avarage, var(--avarage-base)) var(--avarage-l-2) c h
  );
  --shadow-distance: var(--border-size);
  
  border: var(--border-size) solid var(--avarage, var(--avarage-base));
  border-radius: var(--radius);
  background-color: var(--avarage, var(--avarage-base));
  background-image: radial-gradient(
    oklch(from var(--shadow-color) l c h) 1px,
    transparent 0px
  );
  background-repeat: repeat;
  background-size: 7px 7px;
  background-position: center;

  box-shadow: 1px 1px var(--surface), var(--shadow-distance) var(--shadow-distance) var(--shadow-color);
  opacity: min(var(--item-scale), 0.8);
  transform: rotate(var(--deg)) translateY(var(--y)) scale(var(--item-scale))
    rotateX(var(--item-angle));
  transition: all var(--t) ease-in-out;
  will-change: transform;
  transform-style: preserve-3d;
  cursor: pointer;
}

.manga-card:hover {
  --deg: 0 !important;
  --y: -1.25ch;
  --shadow-distance: calc(var(--border-size) * 2);
  opacity: 1;
  z-index: 10;
}

.manga-card figure {
  margin: 0;
}

.manga-card figure img {
  aspect-ratio: var(--ratio);
  object-fit: cover;
  border-radius: calc(var(--radius) - var(--border-size));
  width: 100%;
  height: auto;
  transition: transform var(--t) ease-in-out;
}

.manga-card:hover figure img {
  transform: scale(1.02);
}

.manga-card figure figcaption {
  font-family: Troubleside, Arial, sans-serif;
  margin: 1ch;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-break: break-word;
  line-height: 1.2;
  font-size: 0.9rem;
  font-weight: 500;
}

/* 動畫支援 */
@supports (animation-timeline: view()) {
  .manga-card {
    animation: shrink-top both ease-in-out,
      shrink-bottom both ease-in-out reverse;
    animation-timeline: view(block);
    animation-range: entry, exit;
  }
}

/* 卡片旋轉變化 */
.manga-card:nth-child(2n) {
  --deg: -0.8deg;
}

.manga-card:nth-child(2n + 1) {
  --deg: -0.6deg;
}

.manga-card:nth-child(3n) {
  --deg: 0.6deg;
}

.manga-card:nth-child(4n + 2) {
  --deg: -0.75deg;
}

.manga-card:nth-child(5n) {
  --deg: 0.25deg;
}

.manga-card:nth-child(6n + 3) {
  --deg: -0.3deg;
}

.manga-card:nth-child(7n) {
  --deg: 0.9deg;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .manga-main {
    padding: 1rem;
  }

  .manga-section {
    gap: 1rem;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  }

  .manga-section h1 {
    font-size: 1.5rem;
    margin: 0.5rem 0 0;
  }

  .manga-card figure figcaption {
    font-size: 0.8rem;
    -webkit-line-clamp: 1;
  }
}

@media (max-width: 480px) {
  .manga-main {
    padding: 0.5rem;
  }

  .manga-section {
    gap: 0.75rem;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }

  .manga-section h1 {
    font-size: 1.25rem;
  }

  .manga-card {
    --border-size: 2px;
  }

  .manga-card figure figcaption {
    margin: 0.5ch;
    font-size: 0.75rem;
  }
}

/* 載入動畫 */
.manga-card {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

.manga-card:nth-child(2) { animation-delay: 0.1s; }
.manga-card:nth-child(3) { animation-delay: 0.2s; }
.manga-card:nth-child(4) { animation-delay: 0.3s; }
.manga-card:nth-child(5) { animation-delay: 0.4s; }
.manga-card:nth-child(6) { animation-delay: 0.5s; }
.manga-card:nth-child(7) { animation-delay: 0.6s; }
.manga-card:nth-child(8) { animation-delay: 0.7s; }
.manga-card:nth-child(9) { animation-delay: 0.8s; }

@keyframes fadeInUp {
  to {
    opacity: min(var(--item-scale), 0.8);
    transform: rotate(var(--deg)) translateY(var(--y)) scale(var(--item-scale))
      rotateX(var(--item-angle));
  }
}

/* 焦點狀態 */
.manga-card:focus {
  outline: 2px solid var(--accent);
  outline-offset: 2px;
}

.manga-card:focus:not(:hover) {
  --deg: 0;
  --y: -0.5ch;
  opacity: 1;
}
