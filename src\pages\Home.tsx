import React from 'react';
import './Home.css';

interface MangaItem {
  id: string;
  title: string;
  image: string;
  averageColor: string;
}

const Home: React.FC = () => {
  const readingManga: MangaItem[] = [
    {
      id: '1',
      title: 'Frieren: Beyond Journey\'s End',
      image: 'https://i.imgur.com/HWxOtcQ.jpeg',
      averageColor: '#b0b6a9'
    },
    {
      id: '2',
      title: 'Shinozaki-kun no Mente Jijou',
      image: 'https://i.imgur.com/wRoptbT.png',
      averageColor: '#afa294'
    },
    {
      id: '3',
      title: 'Bibliomania',
      image: 'https://i.imgur.com/MwRrRSd.jpeg',
      averageColor: '#3c3c3d'
    },
    {
      id: '4',
      title: '<PERSON>dad<PERSON>',
      image: 'https://i.imgur.com/7FQ6L5j.jpeg',
      averageColor: '#b47460'
    },
    {
      id: '5',
      title: 'The Summer Hikaru Died',
      image: 'https://i.imgur.com/IQSq88g.jpeg',
      averageColor: '#60a6ce'
    },
    {
      id: '6',
      title: 'The Color of the End: Mission in the Apocalypse',
      image: 'https://i.imgur.com/QfF46xU.jpeg',
      averageColor: '#46666f'
    },
    {
      id: '7',
      title: 'Smoking Behind the Supermarket with You',
      image: 'https://i.imgur.com/jcgbHCO.jpeg',
      averageColor: '#8e898f'
    },
    {
      id: '8',
      title: 'Another',
      image: 'https://i.imgur.com/lIPenqN.jpeg',
      averageColor: '#8d516e'
    }
  ];

  const completedManga: MangaItem[] = [
    {
      id: '9',
      title: 'My Broken Mariko',
      image: 'https://i.imgur.com/OS0VRhm.png',
      averageColor: '#6e695e'
    },
    {
      id: '10',
      title: 'Adabana',
      image: 'https://i.imgur.com/uqktm8j.jpeg',
      averageColor: '#b16e79'
    },
    {
      id: '11',
      title: 'Yiska',
      image: 'https://i.imgur.com/QKXIJlH.jpeg',
      averageColor: '#bdbdbd'
    }
  ];

  const planningManga: MangaItem[] = [
    {
      id: '12',
      title: 'BLAME!',
      image: 'https://i.imgur.com/yCBmW1b.png',
      averageColor: '#7b4d35'
    },
    {
      id: '13',
      title: 'I Have a Crush at Work',
      image: 'https://i.imgur.com/ZGvNhE7.jpeg',
      averageColor: '#ceb5a8'
    },
    {
      id: '14',
      title: 'Carnelian: the Sille Dragon Odyssey',
      image: 'https://i.imgur.com/kTmZvmd.jpeg',
      averageColor: '#6d413f'
    },
    {
      id: '15',
      title: 'Ougon no Keikenchi',
      image: 'https://i.imgur.com/jXc2WJf.jpeg',
      averageColor: '#666060'
    },
    {
      id: '16',
      title: 'Cigarette & Cherry',
      image: 'https://i.imgur.com/fHFUOYg.jpeg',
      averageColor: '#827d88'
    }
  ];

  const MangaSection: React.FC<{ title: string; items: MangaItem[] }> = ({ title, items }) => (
    <section className="manga-section">
      <header>
        <h1>{title}</h1>
      </header>
      {items.map((manga, index) => (
        <article 
          key={manga.id}
          className={`manga-card manga-card-${index + 1}`}
          style={{ '--avarage-color': manga.averageColor } as React.CSSProperties}
        >
          <figure>
            <img src={manga.image} alt={manga.title} loading="lazy" />
            <figcaption>{manga.title}</figcaption>
          </figure>
        </article>
      ))}
    </section>
  );

  return (
    <div className="home-page">
      <main className="manga-main">
        <MangaSection title="Reading" items={readingManga} />
        <MangaSection title="Completed" items={completedManga} />
        <MangaSection title="Planning" items={planningManga} />
      </main>
    </div>
  );
};

export default Home;
