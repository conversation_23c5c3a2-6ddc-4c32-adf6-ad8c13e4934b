/* Subscriptions Page Styles */

.subscriptions-page {
  min-height: 100vh;
  padding: 2rem;
}

.subscriptions-container {
  max-width: 1200px;
  margin: 0 auto;
}

.subscriptions-header {
  text-align: center;
  margin-bottom: 3rem;
}

.subscriptions-header h1 {
  color: var(--accent);
  font-family: Troubleside, Arial, sans-serif;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.subscriptions-header p {
  color: var(--text);
  opacity: 0.8;
  font-size: 1.1rem;
}

.subscriptions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

.subscription-card {
  --avarage: oklch(from var(--avarage-color) var(--avarage-l) 0.0192 h);
  --shadow-color: oklch(from var(--avarage, var(--avarage-base)) var(--avarage-l-2) c h);
  
  background: var(--surface);
  border: var(--border-size) solid var(--avarage, var(--avarage-base));
  border-radius: var(--radius);
  overflow: hidden;
  transition: all var(--t) ease-in-out;
  position: relative;
}

.subscription-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px oklch(from var(--avarage, var(--avarage-base)) 0.3 c h / 0.3);
}

.new-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #ff4757;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: bold;
  z-index: 10;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.subscription-card .card-image {
  aspect-ratio: var(--ratio);
  overflow: hidden;
}

.subscription-card .card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--t) ease-in-out;
}

.subscription-card:hover .card-image img {
  transform: scale(1.05);
}

.subscription-card .card-content {
  padding: 1.5rem;
}

.subscription-card .card-content h3 {
  font-family: Troubleside, Arial, sans-serif;
  font-size: 1.2rem;
  margin-bottom: 0.75rem;
  color: var(--text);
  line-height: 1.3;
}

.latest-chapter {
  color: var(--accent);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.update-date {
  color: var(--text);
  opacity: 0.7;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.card-actions {
  display: flex;
  gap: 0.75rem;
}

.read-btn, .unsubscribe-btn {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: var(--radius);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--t) ease-in-out;
}

.read-btn {
  background: var(--accent);
  color: var(--surface);
}

.read-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px oklch(from var(--accent) 0.3 c h / 0.3);
}

.unsubscribe-btn {
  background: transparent;
  color: #ff6b6b;
  border: 1px solid #ff6b6b;
}

.unsubscribe-btn:hover {
  background: #ff6b6b;
  color: white;
}

.empty-subscriptions {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--text);
}



.empty-subscriptions h3 {
  font-family: Troubleside, Arial, sans-serif;
  font-size: 1.8rem;
  margin-bottom: 1rem;
  opacity: 0.8;
}

.empty-subscriptions p {
  font-size: 1.1rem;
  opacity: 0.6;
  margin-bottom: 2rem;
}

.browse-link {
  display: inline-block;
  padding: 1rem 2rem;
  background: var(--accent);
  color: var(--surface);
  border-radius: var(--radius);
  font-weight: 600;
  text-decoration: none;
  transition: all var(--t) ease-in-out;
}

.browse-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px oklch(from var(--accent) 0.3 c h / 0.3);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .subscriptions-page {
    padding: 1rem;
  }

  .subscriptions-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .subscriptions-header h1 {
    font-size: 2rem;
  }

  .card-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .subscription-card .card-content {
    padding: 1rem;
  }

  .subscriptions-header h1 {
    font-size: 1.5rem;
  }

  .empty-subscriptions {
    padding: 2rem 1rem;
  }


}
