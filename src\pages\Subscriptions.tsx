import React from 'react';
import './Subscriptions.css';

const Subscriptions: React.FC = () => {
  const subscribedManga = [
    {
      id: '1',
      title: 'Frieren: Beyond Journey\'s End',
      image: 'https://i.imgur.com/HWxOtcQ.jpeg',
      averageColor: '#b0b6a9',
      latestChapter: '第 128 話',
      updateDate: '2024-01-15',
      isNew: true
    },
    {
      id: '2',
      title: '<PERSON><PERSON><PERSON>',
      image: 'https://i.imgur.com/7FQ6L5j.jpeg',
      averageColor: '#b47460',
      latestChapter: '第 142 話',
      updateDate: '2024-01-12',
      isNew: true
    }
  ];

  return (
    <div className="subscriptions-page">
      <div className="subscriptions-container">
        <header className="subscriptions-header">
          <h1>我的訂閱</h1>
          <p>追蹤您喜愛的漫畫最新更新</p>
        </header>

        {subscribedManga.length > 0 ? (
          <div className="subscriptions-grid">
            {subscribedManga.map((manga) => (
              <article
                key={manga.id}
                className="subscription-card"
                style={{ '--avarage-color': manga.averageColor } as React.CSSProperties}
              >
                {manga.isNew && <div className="new-badge">新</div>}
                <div className="card-image">
                  <img src={manga.image} alt={manga.title} />
                </div>
                <div className="card-content">
                  <h3>{manga.title}</h3>
                  <p className="latest-chapter">{manga.latestChapter}</p>
                  <p className="update-date">更新於 {manga.updateDate}</p>
                  <div className="card-actions">
                    <button className="read-btn">閱讀</button>
                    <button className="unsubscribe-btn">取消訂閱</button>
                  </div>
                </div>
              </article>
            ))}
          </div>
        ) : (
          <div className="empty-subscriptions">
            <h3>還沒有訂閱任何漫畫</h3>
            <p>去瀏覽頁面找找喜歡的漫畫吧！</p>
            <a href="/browse" className="browse-link">開始瀏覽</a>
          </div>
        )}
      </div>
    </div>
  );
};

export default Subscriptions;
