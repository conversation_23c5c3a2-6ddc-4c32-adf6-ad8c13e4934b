/* Terms Page Styles */

.terms-page {
  min-height: 100vh;
  padding: 2rem;
}

.terms-container {
  max-width: 800px;
  margin: 0 auto;
}

.terms-header {
  text-align: center;
  margin-bottom: 3rem;
}

.terms-header h1 {
  color: var(--accent);
  font-family: Troubleside, Arial, sans-serif;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.terms-header p {
  color: var(--text);
  opacity: 0.6;
  font-size: 0.9rem;
}

.terms-content {
  background: var(--surface);
  border: var(--border-size) solid oklch(from var(--accent) l c h / 0.2);
  border-radius: var(--radius);
  padding: 2.5rem;
}

.terms-section {
  margin-bottom: 2.5rem;
}

.terms-section:last-child {
  margin-bottom: 0;
}

.terms-section h2 {
  color: var(--accent);
  font-family: Troubleside, Arial, sans-serif;
  font-size: 1.4rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid oklch(from var(--accent) l c h / 0.2);
}

.terms-section p {
  color: var(--text);
  opacity: 0.8;
  line-height: 1.7;
  margin-bottom: 1rem;
}

.terms-section ul {
  color: var(--text);
  opacity: 0.8;
  line-height: 1.7;
  padding-left: 1.5rem;
}

.terms-section li {
  margin-bottom: 0.5rem;
}

.terms-section a {
  color: var(--accent);
  text-decoration: none;
}

.terms-section a:hover {
  text-decoration: underline;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .terms-page {
    padding: 1rem;
  }

  .terms-header h1 {
    font-size: 2rem;
  }

  .terms-content {
    padding: 2rem;
  }

  .terms-section h2 {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .terms-header h1 {
    font-size: 1.5rem;
  }

  .terms-content {
    padding: 1.5rem;
  }

  .terms-section h2 {
    font-size: 1.1rem;
  }

  .terms-section ul {
    padding-left: 1rem;
  }
}
