import React from 'react';
import './Terms.css';

const Terms: React.FC = () => {
  return (
    <div className="terms-page">
      <div className="terms-container">
        <header className="terms-header">
          <h1>服務條款</h1>
          <p>最後更新日期：2024年1月1日</p>
        </header>

        <div className="terms-content">
          <section className="terms-section">
            <h2>1. 服務說明</h2>
            <p>
              漫畫屋是一個提供漫畫閱讀服務的平台。使用本服務即表示您同意遵守本服務條款的所有規定。
            </p>
          </section>

          <section className="terms-section">
            <h2>2. 用戶責任</h2>
            <p>用戶在使用本服務時，應當：</p>
            <ul>
              <li>提供真實、準確的個人資訊</li>
              <li>妥善保管帳號密碼，不得與他人共享</li>
              <li>遵守相關法律法規，不得從事違法活動</li>
              <li>尊重智慧財產權，不得非法複製或散布內容</li>
            </ul>
          </section>

          <section className="terms-section">
            <h2>3. 智慧財產權</h2>
            <p>
              本平台上的所有漫畫作品、圖片、文字等內容均受著作權法保護。
              未經授權，用戶不得複製、修改、散布或用於商業用途。
            </p>
          </section>

          <section className="terms-section">
            <h2>4. 服務變更與終止</h2>
            <p>
              我們保留隨時修改、暫停或終止服務的權利。
              如有重大變更，我們會提前通知用戶。
            </p>
          </section>

          <section className="terms-section">
            <h2>5. 免責聲明</h2>
            <p>
              本服務按「現狀」提供，我們不對服務的可用性、準確性或完整性做出保證。
              用戶使用本服務的風險由用戶自行承擔。
            </p>
          </section>

          <section className="terms-section">
            <h2>6. 聯絡資訊</h2>
            <p>
              如對本服務條款有任何疑問，請聯絡我們：
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
          </section>
        </div>
      </div>
    </div>
  );
};

export default Terms;
